from fastapi import APIRouter, HTTPException, UploadFile, File, Form
from typing import Optional
import httpx
import os
import aiofiles
from datetime import datetime
from pydantic import BaseModel
from app.core.dependency import DependAuth
from app.log import logger
from app.settings.config import settings
from app.schemas.base import Success, Fail

router = APIRouter()


# 文件存储相关配置
UPLOAD_DIR = os.path.join(settings.BASE_DIR, "uploads")
API_KEY = "sk-Z3Snt9PE2nfO5CV6yJNcVHCY"  # 建议从环境变量获取


async def ensure_upload_dir():
    """确保上传目录存在"""
    today = datetime.now().strftime("%Y/%m/%d")
    upload_path = os.path.join(UPLOAD_DIR, today)
    os.makedirs(upload_path, exist_ok=True)
    return upload_path


async def save_uploaded_file(file: UploadFile, upload_path: str) -> str:
    """保存上传的文件"""
    # 生成唯一文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    unique_filename = f"{timestamp}_{file.filename}"
    file_path = os.path.join(upload_path, unique_filename)

    # 异步保存文件
    async with aiofiles.open(file_path, 'wb') as f:
        content = await file.read()
        await f.write(content)

    return file_path


class HumanizerRequest(BaseModel):
    text: str
    lang: str = "Chinese"
    type: str = "zhiwang"


# 响应数据模型
class HumanizerData(BaseModel):
    text: str = ""
    original_length: int = 0
    humanized_length: int = 0


class FileUploadData(BaseModel):
    user_doc_id: str = ""
    file_path: str = ""
    status: str = ""  # processing, completed, error


class FileProcessStatusData(BaseModel):
    status: str = ""  # processing, completed, error
    progress: Optional[int] = None
    user_doc_id: str = ""


class FileDownloadRequest(BaseModel):
    user_doc_id: str
    file_name: str = "processed_document"


@router.post("/deai", summary="降AIGC率", dependencies=[DependAuth])
async def deai_text(
    request: HumanizerRequest,
):
    """
    使用 HumanizerAI 降低 AIGC 检测率
    """
    try:
        # 验证输入
        if not request.text or not request.text.strip():
            raise HTTPException(status_code=400, detail="文本内容不能为空")

        if len(request.text) > 5000:
            raise HTTPException(status_code=400, detail="文本长度不能超过5000字符")

        # 调用 HumanizerAI API
        api_url = "https://api3.speedai.chat/v1/deai"

        # 注意：这里需要替换为实际的 API Key
        # 建议从环境变量或配置文件中获取
        api_key = "sk-Z3Snt9PE2nfO5CV6yJNcVHCY"  # 请替换为实际的 API Key

        payload = {
            "apikey": api_key,
            "info": request.text,
            "lang": request.lang,
            "type": request.type
        }

        headers = {
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }

        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(api_url, json=payload, headers=headers)

            if response.status_code != 200:
                logger.error(f"HumanizerAI API 调用失败: {response.status_code} - {response.text}")
                raise HTTPException(status_code=500, detail="API 调用失败")

            result = response.json()

            # 根据 API 返回结果调整响应格式
            if result.get("success") or result.get("code") == 200:
                humanized_text = result.get("data", {}).get("text", "") or result.get("text", "")

                data = HumanizerData(
                    text=humanized_text,
                    original_length=len(request.text),
                    humanized_length=len(humanized_text)
                )
                return Success(data=data.model_dump(), msg="降AIGC率处理成功")
            else:
                error_msg = result.get("message", "未知错误")
                logger.error(f"HumanizerAI API 返回错误: {error_msg}")
                return Fail(msg=f"处理失败: {error_msg}")

    except httpx.TimeoutException:
        logger.error("HumanizerAI API 请求超时")
        raise HTTPException(status_code=500, detail="请求超时，请稍后重试")
    except httpx.RequestError as e:
        logger.error(f"HumanizerAI API 请求错误: {str(e)}")
        raise HTTPException(status_code=500, detail="网络请求错误")
    except Exception as e:
        logger.error(f"降AIGC率处理异常: {str(e)}")
        raise HTTPException(status_code=500, detail="服务器内部错误")


@router.post("/rewrite", summary="文本重写", dependencies=[DependAuth])
async def rewrite_text(
    request: HumanizerRequest,
):
    """
    使用 HumanizerAI 重写文本
    """
    try:
        # 验证输入
        if not request.text or not request.text.strip():
            raise HTTPException(status_code=400, detail="文本内容不能为空")

        if len(request.text) > 5000:
            raise HTTPException(status_code=400, detail="文本长度不能超过5000字符")

        # 调用 HumanizerAI API
        api_url = "https://api3.speedai.chat/v1/rewrite"

        # 注意：这里需要替换为实际的 API Key
        api_key = "test_api"  # 请替换为实际的 API Key

        payload = {
            "apikey": api_key,
            "info": request.text,
            "lang": request.lang,
            "type": request.type
        }

        headers = {
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }

        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(api_url, json=payload, headers=headers)

            if response.status_code != 200:
                logger.error(f"HumanizerAI API 调用失败: {response.status_code} - {response.text}")
                raise HTTPException(status_code=500, detail="API 调用失败")

            result = response.json()

            # 根据 API 返回结果调整响应格式
            if result.get("success") or result.get("code") == 200:
                rewritten_text = result.get("data", {}).get("text", "") or result.get("text", "")

                data = HumanizerData(
                    text=rewritten_text,
                    original_length=len(request.text),
                    humanized_length=len(rewritten_text)
                )
                return Success(data=data.model_dump(), msg="文本重写处理成功")
            else:
                error_msg = result.get("message", "未知错误")
                logger.error(f"HumanizerAI API 返回错误: {error_msg}")
                return Fail(msg=f"处理失败: {error_msg}")

    except httpx.TimeoutException:
        logger.error("HumanizerAI API 请求超时")
        raise HTTPException(status_code=500, detail="请求超时，请稍后重试")
    except httpx.RequestError as e:
        logger.error(f"HumanizerAI API 请求错误: {str(e)}")
        raise HTTPException(status_code=500, detail="网络请求错误")
    except Exception as e:
        logger.error(f"文本重写处理异常: {str(e)}")
        raise HTTPException(status_code=500, detail="服务器内部错误")


@router.post("/upload", summary="上传文档进行处理", dependencies=[DependAuth])
async def upload_document(
    file: UploadFile = File(...),
    mode: str = Form("deai", description="处理模式: deai(降AIGC率) 或 rewrite(重写)"),
    type_: str = Form("zhiwang", description="处理类型: zhiwang, weipu, gezida"),
    changed_only: bool = Form(True, description="是否仅返回修改部分"),
    skip_english: bool = Form(False, description="是否跳过英文部分"),
):
    """
    上传 DOCX 文档并调用 HumanizerAI 进行处理
    """
    try:
        # 验证文件格式
        if not file.filename.lower().endswith('.docx'):
            raise HTTPException(status_code=400, detail="仅支持 .docx 格式文件")

        # 验证文件大小 (限制为 10MB)
        file_content = await file.read()
        if len(file_content) > 10 * 1024 * 1024:
            raise HTTPException(status_code=400, detail="文件大小不能超过 10MB")

        # 重置文件指针
        await file.seek(0)

        # 确保上传目录存在
        upload_path = await ensure_upload_dir()

        # 保存文件
        file_path = await save_uploaded_file(file, upload_path)

        # 调用 HumanizerAI 文档处理 API
        api_url = "https://api3.speedai.chat/v1/docx"

        # 重新读取文件内容用于上传
        async with aiofiles.open(file_path, 'rb') as f:
            file_content = await f.read()

        # 准备表单数据
        files = {
            'file': (file.filename, file_content, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')
        }

        data = {
            'FileName': file.filename,
            'username': API_KEY,
            'mode': mode,
            'type_': type_,
            'changed_only': str(changed_only).lower(),
            'skip_english': str(skip_english).lower()
        }

        headers = {
            "Authorization": f"Bearer {API_KEY}",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }

        async with httpx.AsyncClient(timeout=120.0) as client:
            response = await client.post(api_url, data=data, files=files, headers=headers)

            if response.status_code != 200:
                logger.error(f"HumanizerAI 文档上传失败: {response.status_code} - {response.text}")
                raise HTTPException(status_code=500, detail="文档上传失败")

            result = response.json()

            if result.get("status") == "processing":
                data = FileUploadData(
                    user_doc_id=result.get("user_doc_id", ""),
                    file_path=file_path,
                    status="processing"
                )
                return Success(data=data.model_dump(), msg="文档上传成功，开始处理")
            else:
                error_msg = result.get("error", "未知错误")
                logger.error(f"HumanizerAI 文档处理错误: {error_msg}")
                return Fail(msg=f"文档处理失败: {error_msg}")

    except httpx.TimeoutException:
        logger.error("HumanizerAI 文档上传请求超时")
        raise HTTPException(status_code=500, detail="请求超时，请稍后重试")
    except httpx.RequestError as e:
        logger.error(f"HumanizerAI 文档上传请求错误: {str(e)}")
        raise HTTPException(status_code=500, detail="网络请求错误")
    except Exception as e:
        logger.error(f"文档上传处理异常: {str(e)}")
        raise HTTPException(status_code=500, detail="服务器内部错误")


@router.post("/status", summary="查询文档处理状态", dependencies=[DependAuth])
async def check_document_status(
    user_doc_id: str = Form(..., description="文档处理ID"),
):
    """
    查询文档处理状态
    """
    try:
        # 调用 HumanizerAI 状态查询 API
        api_url = "https://api3.speedai.chat/v1/docx/status"

        payload = {
            "user_doc_id": user_doc_id
        }

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {API_KEY}",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }

        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(api_url, json=payload, headers=headers)

            if response.status_code != 200:
                logger.error(f"HumanizerAI 状态查询失败: {response.status_code} - {response.text}")
                raise HTTPException(status_code=500, detail="状态查询失败")

            result = response.json()

            status = result.get("status", "unknown")
            progress = result.get("progress")

            if status == "completed":
                data = FileProcessStatusData(
                    status=status,
                    progress=100,
                    user_doc_id=user_doc_id
                )
                return Success(data=data.model_dump(), msg="文档处理完成")
            elif status == "processing":
                data = FileProcessStatusData(
                    status=status,
                    progress=progress,
                    user_doc_id=user_doc_id
                )
                return Success(data=data.model_dump(), msg=f"文档处理中，进度: {progress}%")
            elif status == "error":
                error_msg = result.get("error", "处理失败")
                return Fail(msg=f"处理失败: {error_msg}")
            else:
                return Fail(msg="未知状态")

    except httpx.TimeoutException:
        logger.error("HumanizerAI 状态查询请求超时")
        raise HTTPException(status_code=500, detail="请求超时，请稍后重试")
    except httpx.RequestError as e:
        logger.error(f"HumanizerAI 状态查询请求错误: {str(e)}")
        raise HTTPException(status_code=500, detail="网络请求错误")
    except Exception as e:
        logger.error(f"状态查询处理异常: {str(e)}")
        raise HTTPException(status_code=500, detail="服务器内部错误")


@router.post("/download", summary="下载处理后的文档", dependencies=[DependAuth])
async def download_processed_document(
    request: FileDownloadRequest,
):
    """
    下载处理后的文档
    """
    try:
        # 调用 HumanizerAI 文件下载 API
        api_url = "https://api3.speedai.chat/v1/download"

        payload = {
            "user_doc_id": request.user_doc_id,
            "file_name": request.file_name
        }

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {API_KEY}",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }

        async with httpx.AsyncClient(timeout=120.0) as client:
            response = await client.post(api_url, json=payload, headers=headers)

            if response.status_code != 200:
                logger.error(f"HumanizerAI 文件下载失败: {response.status_code} - {response.text}")
                raise HTTPException(status_code=404, detail="文件未找到或下载失败")

            # 返回文件内容
            from fastapi.responses import Response
            import urllib.parse

            # 对文件名进行URL编码以支持中文
            encoded_filename = urllib.parse.quote(f"{request.file_name}.docx", safe='')

            return Response(
                content=response.content,
                media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                headers={
                    "Content-Disposition": f"attachment; filename*=UTF-8''{encoded_filename}"
                }
            )

    except httpx.TimeoutException:
        logger.error("HumanizerAI 文件下载请求超时")
        raise HTTPException(status_code=500, detail="请求超时，请稍后重试")
    except httpx.RequestError as e:
        logger.error(f"HumanizerAI 文件下载请求错误: {str(e)}")
        raise HTTPException(status_code=500, detail="网络请求错误")
    except Exception as e:
        logger.error(f"文件下载处理异常: {str(e)}")
        raise HTTPException(status_code=500, detail="服务器内部错误")