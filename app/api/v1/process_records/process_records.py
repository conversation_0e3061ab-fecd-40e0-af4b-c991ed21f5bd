from fastapi import APIRouter, Query, Depends
from typing import Optional

from app.controllers.process_record import process_record_controller
from app.core.dependency import DependAuth, get_current_user
from app.models.admin import User
from app.schemas import Success
from app.schemas.process_records import (
    ProcessRecordQuery,
    ProcessRecordListResponse,
    ProcessRecordResponse
)

router = APIRouter()


@router.get("/list", summary="获取处理记录列表")
async def list_process_records(
    status: Optional[str] = Query(None, description="处理状态"),
    process_mode: Optional[str] = Query(None, description="处理模式"),
    process_type: Optional[str] = Query(None, description="处理类型"),
    page: int = Query(1, description="页码", ge=1),
    page_size: int = Query(10, description="每页数量", ge=1, le=100),
    current_user: User = Depends(get_current_user)
):
    """获取当前用户的处理记录列表"""
    query = ProcessRecordQuery(
        user_id=current_user.id,
        status=status,
        process_mode=process_mode,
        process_type=process_type,
        page=page,
        page_size=page_size
    )
    
    result = await process_record_controller.get_user_records(current_user.id, query)
    
    # 转换为响应格式
    items = []
    for record in result["items"]:
        record_dict = await record.to_dict()
        items.append(ProcessRecordResponse(**record_dict))
    
    response = ProcessRecordListResponse(
        items=items,
        total=result["total"],
        page=result["page"],
        page_size=result["page_size"],
        total_pages=result["total_pages"]
    )
    
    return Success(data=response.model_dump())


@router.get("/stats", summary="获取处理记录统计")
async def get_process_stats(
    current_user: User = Depends(get_current_user)
):
    """获取当前用户的处理记录统计信息"""
    stats = await process_record_controller.get_user_stats(current_user.id)
    return Success(data=stats)


@router.get("/{record_id}", summary="获取处理记录详情")
async def get_process_record(
    record_id: int,
    current_user: User = Depends(get_current_user)
):
    """获取处理记录详情"""
    record = await process_record_controller.get(id=record_id)
    
    # 检查权限
    if record.user_id != current_user.id:
        return Success(code=403, msg="无权限访问")
    
    record_dict = await record.to_dict()
    response = ProcessRecordResponse(**record_dict)
    
    return Success(data=response.model_dump())


@router.post("/{record_id}/download", summary="记录下载操作")
async def record_download(
    record_id: int,
    current_user: User = Depends(get_current_user)
):
    """记录下载操作，增加下载次数"""
    record = await process_record_controller.get(id=record_id)
    
    # 检查权限
    if record.user_id != current_user.id:
        return Success(code=403, msg="无权限访问")
    
    # 增加下载次数
    updated_record = await process_record_controller.increment_download_count(record.user_doc_id)
    
    if updated_record:
        return Success(msg="下载记录更新成功")
    else:
        return Success(code=404, msg="记录未找到")
