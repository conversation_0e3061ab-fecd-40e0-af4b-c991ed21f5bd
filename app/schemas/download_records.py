from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field


class BaseDownloadRecord(BaseModel):
    user_doc_id: str = Field(..., description="文档处理ID", example="doc_123456")
    status: str = Field("processing", description="处理状态", example="processing")
    file_name: str = Field(..., description="原始文件名", example="document.docx")
    file_size: Optional[int] = Field(None, description="文件大小(字节)", example=1024000)
    process_mode: str = Field(..., description="处理模式", example="deai")
    process_type: str = Field(..., description="处理类型", example="zhiwang")
    language: str = Field("Chinese", description="语言", example="Chinese")
    error_message: Optional[str] = Field(None, description="错误信息")


class DownloadRecordCreate(BaseDownloadRecord):
    user_id: int = Field(..., description="用户ID", example=1)


class DownloadRecordUpdate(BaseModel):
    id: int = Field(..., description="记录ID")
    status: Optional[str] = Field(None, description="处理状态")
    error_message: Optional[str] = Field(None, description="错误信息")
    download_count: Optional[int] = Field(None, description="下载次数")
    last_download_at: Optional[datetime] = Field(None, description="最后下载时间")

    def update_dict(self):
        return self.model_dump(exclude_unset=True, exclude={"id"})


class DownloadRecordResponse(BaseDownloadRecord):
    id: int = Field(..., description="记录ID")
    user_id: int = Field(..., description="用户ID")
    download_count: int = Field(0, description="下载次数")
    last_download_at: Optional[datetime] = Field(None, description="最后下载时间")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


class DownloadRecordQuery(BaseModel):
    user_id: Optional[int] = Field(None, description="用户ID")
    status: Optional[str] = Field(None, description="处理状态")
    process_mode: Optional[str] = Field(None, description="处理模式")
    process_type: Optional[str] = Field(None, description="处理类型")
    page: int = Field(1, description="页码", ge=1)
    page_size: int = Field(10, description="每页数量", ge=1, le=100)


class DownloadRecordListResponse(BaseModel):
    items: list[DownloadRecordResponse] = Field(..., description="记录列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量")
    total_pages: int = Field(..., description="总页数")
